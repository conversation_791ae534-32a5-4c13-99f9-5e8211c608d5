# ChartFix Discord Bot

## System Overview

ChartFix is a production-ready Discord bot designed for cryptocurrency market monitoring, portfolio management, and **automated Binance Futures trading**. The system provides real-time market data aggregation, economic calendar integration, portfolio tracking, intelligent alert systems, and comprehensive trading capabilities through Discord's interface. Built with clean architecture principles, the bot serves as a complete financial trading platform for cryptocurrency enthusiasts, traders, and investors.

### Primary Purpose

The system centralizes cryptocurrency market intelligence and trading operations by integrating multiple data sources (Binance, CoinGecko, Forex Factory) and delivering actionable insights through Discord's native interface. It transforms raw market data into structured, real-time notifications, interactive dashboards, and automated trading capabilities accessible within Discord servers. The bot enables users to monitor markets, manage portfolios, and execute trades directly through Discord commands.

## Architecture

### System Architecture

ChartFix implements a clean, layered architecture with clear separation of concerns. The system is organized into distinct layers that handle different aspects of functionality, from data persistence to user interface interactions and automated trading operations.

```
📁 ChartFix Discord Bot
├── 🤖 bot.py                         # Main application entry point & Discord client
├── 📊 services/                      # Business logic layer (organized by functionality)
│   ├── 🔥 trading/                   # **Trading & Order Management**
│   │   ├── trading_service.py        # **Binance Futures trading engine**
│   │   ├── order_tracker.py          # **Order tracking & fill monitoring**
│   │   ├── position_manager.py       # **Position management & P&L tracking**
│   │   ├── risk_manager.py           # **Risk management & margin monitoring**
│   │   ├── analytics_engine.py       # **Trading analytics & performance metrics**
│   │   ├── notification_service.py   # **Trading notifications & alerts**
│   │   └── websocket_monitor.py      # **Real-time WebSocket monitoring**
│   ├── 📈 market/                    # **Market Data & Analysis**
│   │   ├── market_service.py         # Market data aggregation & watchlist management
│   │   ├── market_monitor_service.py # Real-time market monitoring & alerts
│   │   ├── trading_time_service.py   # Trading time analysis & volume surge detection
│   │   ├── economic_calendar_service.py # Economic calendar & event notifications
│   │   ├── news_service.py           # News aggregation & market data
│   │   └── chart_service.py          # Chart generation & technical analysis
│   ├── 🗄️ data/                      # **Data Persistence & Caching**
│   │   ├── database_service.py       # Main data persistence & connection pooling
│   │   ├── database.py               # Trading database (separate SQLite instance)
│   │   └── cache_service.py          # Multi-layer caching with TTL management
│   ├── 🤖 discord/                   # **Discord Integration**
│   │   ├── discord_service.py        # Discord API utilities & message formatting
│   │   └── discord_bot_logging.py    # Discord logging & status notifications
│   └── ⚙️ core/                      # **Core Utilities & Services**
│       ├── error_service.py          # Centralized error handling & logging
│       ├── symbol_service.py         # Symbol normalization & validation
│       ├── http_client_service.py    # Centralized HTTP client & connection management
│       └── portfolio_service.py      # Portfolio tracking & P&L calculations
├── 🎮 handlers/discord/              # Discord command interface layer (organized by purpose)
│   ├── 💹 trading/                   # **Trading Commands**
│   │   ├── trading_commands.py       # **Trading commands (/l, /s, /ql, /qs, /scl, /swl)**
│   │   └── advanced_commands.py      # **Advanced trading (/positions, /pnl, /close, /history)**
│   ├── 📊 market/                    # **Market & Portfolio Commands**
│   │   ├── market_commands.py        # Market data & analysis commands
│   │   ├── watchlist_commands.py     # Watchlist management commands
│   │   └── portfolio_commands.py     # Portfolio & investment tracking commands
│   ├── � alerts/                    # **Alert Handlers**
│   │   ├── market_monitor_alerts.py  # Market monitoring alert handlers
│   │   ├── market_news_handler.py    # Market news alert handlers
│   │   ├── trading_time_alerts.py    # Trading time alert handlers
│   │   └── volatility_alerts.py      # Volatility alert handlers
│   └── 🛠️ admin/                     # **Administration**
│       └── admin_commands.py         # System administration commands
├── 🛠️ utils/                         # Utility & helper functions
│   ├── config.py                     # Configuration management
│   ├── ui_components.py              # UI formatting & embed creation
│   ├── constants.py                  # Application constants
│   └── symbol_mappings.py            # Symbol mapping & validation
├── 📝 logs/                          # Structured logging with rotation
├── 🧪 tests/                         # Comprehensive test suite
├── 📋 config.yaml                    # System configuration
├── 🗄️ chartfix.db                    # Main SQLite database
├── 💹 trading.db                     # **Trading SQLite database**
└── 📚 docs/                          # **Documentation & guides**
    └── TRADING_MODULE_GUIDE.md       # **Trading module documentation**
```

### Service Layer Architecture

#### Core Market & Data Services
| Service | Responsibility | Core Capabilities |
|---------|---------------|------------------|
| **market_service** | Market data aggregation | Real-time price feeds, OHLCV data, watchlist management, multi-exchange integration |
| **portfolio_service** | Portfolio management | Position tracking, P&L calculations, trade history, performance analytics |
| **database_service** | Main data persistence | SQLite operations, connection pooling, transaction management, data migrations |
| **database** | Trading data persistence | **Separate trading database, order/position storage, trading history** |
| **discord_service** | Discord API integration | Message formatting, embed creation, UI components, interaction handling |
| **discord_bot_logging** | Discord logging | **Status notifications, error reporting, system alerts via Discord** |
| **error_service** | Error handling | Centralized error processing, logging, recovery mechanisms, fault tolerance |
| **symbol_service** | Symbol processing | Normalization, validation, exchange-specific formatting, symbol mapping |
| **cache_service** | Performance optimization | TTL-based caching, memory management, cache invalidation, multi-layer storage |
| **market_monitor_service** | Real-time monitoring | Volatility detection, price alerts, threshold monitoring, notification dispatch |
| **economic_calendar_service** | Economic data integration | Forex Factory API, event scheduling, impact analysis, automated notifications |
| **news_service** | News aggregation | Market news filtering, sentiment analysis, data correlation, content curation |
| **chart_service** | Technical analysis | Chart generation, indicator calculation, visual data representation |
| **http_client_service** | HTTP management | Centralized aiohttp client, connection pooling, request optimization, leak prevention |

#### 🔥 Trading System Services (Phase 1-3 Implementation)
| Service | Responsibility | Core Capabilities |
|---------|---------------|------------------|
| **🔥 trading_service** | **Binance Futures trading** | **Market/limit orders, hedge mode, TP/SL automation, position management** |
| **📊 order_tracker** | **Order lifecycle management** | **Order status monitoring, fill detection, execution tracking, TP/SL coordination** |
| **💰 position_manager** | **Position & P&L management** | **Real-time position tracking, unrealized/realized P&L, margin calculations** |
| **⚠️ risk_manager** | **Risk management** | **Margin monitoring, position size limits, risk alerts, portfolio protection** |
| **📈 analytics_engine** | **Trading analytics** | **Performance metrics, win/loss ratios, trading statistics, portfolio analysis** |
| **🔔 notification_service** | **Trading notifications** | **Order fill alerts, position updates, P&L notifications, risk warnings** |
| **🌐 websocket_monitor** | **Real-time data streaming** | **WebSocket connections, live price feeds, order book monitoring** |

### Component Interaction

The system follows a layered architecture where:
- **Discord Handlers** interface with users and delegate to services
- **Services** contain business logic and coordinate with external APIs
- **Trading System** manages automated Binance Futures trading operations
- **Utilities** provide cross-cutting concerns and helper functions
- **Databases** persist user data, system state, and trading information
- **Cache** optimizes performance through intelligent data storage

## Core Features

### 🔥 Automated Trading System (NEW)
- **Binance Futures Integration**: Full hedge mode support with market and limit orders
- **Discord Trading Commands**: Execute trades directly through Discord (`/l`, `/s`, `/ql`, `/qs`)
- **Scalping & Swing Trading**: Automated TP/SL with `/scl` (1.5% TP, 1% SL) and `/swl` (8% TP, 5% SL)
- **Position Management**: Real-time position tracking with P&L calculations
- **Order Tracking**: Comprehensive order lifecycle monitoring and fill detection
- **Risk Management**: Margin monitoring, position limits, and automated risk alerts
- **Trading Analytics**: Performance metrics, win/loss ratios, and portfolio analysis
- **Advanced Commands**: `/positions`, `/pnl`, `/close`, `/history` for portfolio management

### Real-time Market Intelligence
- **Live Price Tracking**: Continuous monitoring of cryptocurrency prices with 90-second refresh intervals
- **Volatility Detection**: Multi-timeframe volatility analysis (15m, 1h, daily) with configurable thresholds
- **Market Overview**: Global market statistics, dominance metrics, and trend analysis
- **Rate Monitoring**: Binance Earn rates and P2P USDT/VND price tracking

### Portfolio Management System
- **Position Tracking**: Multi-asset portfolio management with real-time valuation
- **P&L Calculations**: Automated profit/loss calculations with average price tracking
- **Trade History**: Comprehensive trade logging and performance analytics
- **Risk Assessment**: Portfolio diversification and exposure analysis

### Economic Calendar Integration
- **Forex Factory API**: Real-time economic event data with impact analysis
- **Automated Scheduling**: Daily calendar posts and pre-event notifications
- **Event Filtering**: Currency-specific filtering (USD, CNY, JPY) with impact-based prioritization
- **Crypto Correlation**: Analysis of economic events' impact on cryptocurrency markets

### Intelligent Alert System
- **Volume Surge Detection**: Real-time monitoring with multi-level alert thresholds (200%, 500%, 1000%)
- **Trading Time Analysis**: Optimal trading window identification across global market sessions
- **Price Movement Alerts**: Configurable price change notifications with trend analysis
- **Market Session Monitoring**: Asian, European, and US market session tracking

## Technology Stack

### Core Framework
- **Discord.py 2.3+**: Modern Discord API wrapper with slash command support
- **Python 3.8+**: Asynchronous programming with asyncio and type hints
- **aiohttp**: High-performance HTTP client with connection pooling
- **SQLite**: Dual database setup - main database + dedicated trading database
- **WebSockets**: Real-time data streaming for trading operations

### External APIs
- **Binance Futures API**: Real-time cryptocurrency market data and **automated trading**
- **CoinGecko Pro API**: Comprehensive cryptocurrency data and market analytics
- **Forex Factory API**: Economic calendar events and financial news
- **NewsAPI**: Financial news aggregation and sentiment analysis

### Trading & Financial Libraries
- **ccxt**: Unified cryptocurrency exchange API integration for trading
- **pandas**: Data manipulation and analysis for market data processing
- **numpy**: Numerical computing for statistical calculations and analytics
- **mplfinance**: Financial chart generation and technical analysis visualization
- **decimal**: Precise financial calculations for trading operations

### System Infrastructure
- **asyncio**: Asynchronous I/O operations and concurrent task management
- **logging**: Structured logging with rotation and monitoring capabilities
- **yaml**: Configuration management and settings persistence
- **psutil**: System monitoring and performance metrics collection
- **websockets**: Real-time WebSocket connections for live trading data

### Architecture Patterns
- **Clean Architecture**: Separation of concerns with distinct service layers
- **Dependency Injection**: Service-based architecture with singleton patterns
- **Observer Pattern**: Event-driven notifications and alert systems
- **Repository Pattern**: Data access abstraction and persistence management
- **Command Pattern**: Discord command handling with trading operations
- **Strategy Pattern**: Multiple trading strategies (scalping, swing, manual)

## System Design

### Data Flow Architecture

The system operates through a multi-layered data flow that ensures efficient processing and delivery of market intelligence and trading operations:

1. **Data Ingestion Layer**
   - External API polling (Binance, CoinGecko, Forex Factory)
   - Real-time WebSocket connections for trading data
   - Data normalization and validation
   - Rate limiting and error handling for API interactions

2. **Processing Layer**
   - Market data aggregation and analysis
   - Portfolio calculations and P&L tracking
   - Trading order processing and execution
   - Alert threshold evaluation and trigger detection

3. **Trading Layer** 🔥
   - Order placement and tracking
   - Position management and P&L calculations
   - Risk management and margin monitoring
   - Real-time trading notifications

4. **Caching Layer**
   - Multi-tier caching with configurable TTL values
   - Memory-efficient data storage and retrieval
   - Cache invalidation and refresh strategies

5. **Presentation Layer**
   - Discord embed formatting and UI component generation
   - Interactive button and command handling
   - Real-time message updates and notifications

### Service Interaction Patterns

**Market Data Flow:**
```
External APIs → HTTP Client Service → Market Service → Cache Service → Discord Handlers → Discord UI
```

**Portfolio Management:**
```
User Commands → Portfolio Service → Database Service → Market Service → Discord Response
```

**🔥 Trading Flow (NEW):**
```
Discord Commands → Trading Service → Binance API → Order Tracker → Position Manager → Notifications
```

**Alert System:**
```
Market Monitor → Threshold Analysis → Alert Generation → Discord Notifications
```

**🔥 Trading Monitoring:**
```
WebSocket Monitor → Position Manager → Risk Manager → Notification Service → Discord Alerts
```

### Database Schema Design

The system uses **dual SQLite databases** with optimized schemas:

#### Main Database (`chartfix.db`)
- **Users Table**: User identification and preferences
- **Portfolios Table**: Portfolio positions with quantity and average price tracking
- **Trades Table**: Historical trade data for performance analysis
- **Watchlists Table**: User-specific cryptocurrency watchlists
- **System State Table**: Application state and configuration persistence

#### 🔥 Trading Database (`trading.db`) - NEW
- **Orders Table**: Order tracking with status, fills, and metadata
- **Positions Table**: Active positions with real-time P&L tracking
- **Trades Table**: Completed trade history with performance metrics
- **Risk Metrics Table**: Risk management data and margin tracking
- **Analytics Table**: Trading performance statistics and analytics

## 🎮 Discord Commands

### 📊 Market & Portfolio Commands
- `/watchlist` - Display cryptocurrency watchlist with real-time prices
- `/portfolio` - Show portfolio overview with P&L calculations
- `/holdings` - Detailed holdings breakdown
- `/add_trade` - Add manual trade to portfolio
- `/market` - Market data and analysis
- `/rates` - Binance Earn rates and P2P USDT/VND prices
- `/calendar` - Economic calendar events
- `/c [symbol]` - Quick price check
- `/p [symbol]` - Price with additional details
- `/stats` - Bot statistics and performance

### 🔥 Trading Commands (Restricted to #trade channel)
#### Basic Trading
- `/l [symbol] [price] [value/amount] [tp] [sl]` - **Long limit order**
- `/s [symbol] [price] [value/amount] [tp] [sl]` - **Short limit order**
- `/ql [symbol] [value/amount]` - **Quick long** (current price - 0.2%)
- `/qs [symbol] [value/amount]` - **Quick short** (current price + 0.2%)

#### Strategy Trading
- `/scl [symbol] [price] [value]` - **Scalping long** (TP: 1.5%, SL: 1%)
- `/scs [symbol] [price] [value]` - **Scalping short** (TP: 1.5%, SL: 1%)
- `/swl [symbol] [price] [value]` - **Swing long** (TP: 8%, SL: 5%)
- `/sws [symbol] [price] [value]` - **Swing short** (TP: 8%, SL: 5%)

#### Order Management
- `/cancelorder [symbol] [side]` - Cancel all orders for symbol (side: buy/sell, optional)
- `/orders` - View active orders
- `/balance` - Account balance and margin info

> **Note**: Order operations use `buy/sell`, Position operations use `LONG/SHORT`

### 📈 Advanced Trading Commands (Phase 3)
#### Position Management
- `/status` - **Create/update pinned real-time status dashboard**
- `/positions [symbol] [sort_by]` - View all active positions with P&L
- `/pnl [period] [symbol]` - Comprehensive P&L analysis and metrics
- `/history [days] [symbol] [limit]` - Trading history and performance

#### Position Control
- `/tp [symbol] [side] [price] [percentage]` - **Set take profit** (side: LONG/SHORT, percentage: 25/50/75/100%)
- `/sl [symbol] [side] [price] [percentage]` - **Set stop loss** (side: LONG/SHORT, percentage: 25/50/75/100%)
- `/closeall [confirm:yes]` - **Close all positions** (requires confirmation)
- `/closeside [side] [confirm:yes]` - **Close all LONG/SHORT positions** (side: LONG/SHORT)
- `/closepos [symbol] [side] [percentage] [confirm:yes]` - **Close specific position** (side: LONG/SHORT)

### 🛠️ Admin Commands
- `/pin [message]` - Pin important message
- `/unpin [message_id]` - Unpin message
- `/pins` - List pinned messages

### Concurrency and Performance

**Asynchronous Operations:**
- Non-blocking API calls with connection pooling
- Concurrent data processing across multiple services
- Background task scheduling for automated features
- Real-time WebSocket connections for trading data

**Performance Optimizations:**
- Intelligent caching with TTL-based expiration
- Dual database setup for optimized data access
- Database connection pooling with WAL mode
- Memory-efficient data structures and processing
- Optimized trading order execution

**Error Handling:**
- Graceful degradation with fallback mechanisms
- Centralized error logging and monitoring
- Automatic retry logic with exponential backoff
- Trading-specific error recovery and position safety

### Security and Reliability

**Security Architecture:**
- Input validation and sanitization across all user interfaces
- API key management with secure configuration handling
- Rate limiting and abuse protection mechanisms
- Error isolation to prevent system-wide failures
- **Trading channel restrictions** for security
- **Position size limits** and risk management

**Reliability Features:**
- Automatic error recovery with graceful degradation
- Connection pooling for database and HTTP operations
- Comprehensive logging with rotation and monitoring
- Health monitoring and status reporting capabilities
- **Trading order validation** and safety checks
- **Dual database backup** for data integrity

**Performance Characteristics:**
- Sub-2-second response times for most operations
- **Sub-1-second trading order execution**
- Optimized memory footprint through efficient caching
- 99.9% availability target with comprehensive error handling
- Intelligent API usage to minimize external dependencies
- **Real-time position tracking** and P&L updates

### Architecture Principles

The system is built on established software engineering principles:

**Clean Architecture:**
- Clear separation of concerns across distinct service layers
- Single responsibility principle with each service having one clear purpose
- Dependency injection for testability and flexibility
- Standardized interfaces and error handling patterns

**Performance Optimization:**
- Consolidated service architecture eliminating code duplication
- Unified caching strategy with intelligent TTL management
- Single exchange instance with connection pooling
- Asynchronous operations throughout the system
- **Optimized trading execution pipeline**

**Maintainability:**
- Clean import structure without circular dependencies
- Consistent error handling and return formats
- Comprehensive logging and monitoring capabilities
- Modular design enabling easy extension and modification
- **Comprehensive test coverage** for trading operations

**🔥 Trading System Principles:**
- **Phase-based implementation** (Core → Position Management → Advanced Features)
- **Risk-first approach** with comprehensive safety mechanisms
- **Real-time monitoring** and instant notifications
- **Scalable architecture** supporting multiple trading strategies
- **Data integrity** with dual database design

---

## 🚀 Getting Started

### Prerequisites
- Python 3.8+
- Discord Bot Token
- Binance Futures API credentials
- Required Python packages (see `requirements.txt`)

### Installation
1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Configure `config.yaml` with your API credentials
4. Run the bot: `python bot.py`

### Trading Setup
1. Create a `#trade` channel in your Discord server
2. Configure Binance Futures API with appropriate permissions
3. Set up hedge mode in your Binance Futures account
4. Test with small amounts before full deployment

For detailed trading module documentation, see [`docs/TRADING_MODULE_GUIDE.md`](docs/TRADING_MODULE_GUIDE.md)

---


