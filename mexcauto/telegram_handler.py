import requests
import logging
import time
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timezone
import threading
import queue
import json
from telebot.types import ReplyKeyboardMarkup, KeyboardButton

# Cấu hình logger cho module telegram
logger = logging.getLogger('telegram')

class TelegramHandler:
    def __init__(self, token: str, admin_chat_id: str):
        self.token = token
        self.admin_chat_id = admin_chat_id
        self.base_url = f"https://api.telegram.org/bot{token}"
        self.command_handlers = {}  # Đăng ký các command handlers
        self.last_update_id = 0
        self.message_queue = queue.Queue()
        self.stop_polling = False
        self.polling_thread = None

        # Đăng ký các lệnh mặc định
        self.register_command('start', self._handle_start)
        self.register_command('help', self._handle_help)
        self.register_command('keyboard', self._handle_keyboard)

        logger.info("TelegramHandler đã được khởi tạo")

    def register_command(self, command: str, handler: Callable):
        """<PERSON><PERSON>ng ký một command handler"""
        self.command_handlers[command] = handler
        logger.debug(f"Đã đăng ký handler cho lệnh: {command}")

    def send_message(self, message: str, chat_id: Optional[str] = None, reply_markup: Optional[dict] = None) -> bool:
        """
        Gửi tin nhắn đến chat_id (mặc định là admin)

        Args:
            message: Nội dung tin nhắn
            chat_id: ID của chat cần gửi (mặc định là admin_id)
            reply_markup: Bàn phím trả lời (nếu có)

        Returns:
            bool: True nếu gửi thành công, False nếu thất bại
        """
        if not self.token:
            logger.warning("Thiếu token Telegram")
            return False

        if not chat_id:
            chat_id = self.admin_chat_id

        # Đảm bảo tin nhắn không quá dài
        if len(message) > 4096:
            message = message[:4093] + "..."

        url = f"{self.base_url}/sendMessage"
        payload = {"chat_id": chat_id, "text": message, "parse_mode": "HTML"}

        # Thêm reply_markup nếu có
        if reply_markup:
            payload["reply_markup"] = json.dumps(reply_markup)

        try:
            response = requests.post(url, json=payload, timeout=15)
            response.raise_for_status()
            return True
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn Telegram: {e}")
            return False

    def create_reply_keyboard(self, commands: List[List[str]], resize_keyboard: bool = True,
                             one_time_keyboard: bool = False) -> dict:
        """
        Tạo reply keyboard với các lệnh được cung cấp

        Args:
            commands: Danh sách các lệnh, mỗi phần tử là một hàng trên bàn phím
            resize_keyboard: Tự động điều chỉnh kích thước bàn phím
            one_time_keyboard: Bàn phím sẽ biến mất sau khi sử dụng

        Returns:
            Dict chứa cấu trúc reply_markup cho API Telegram
        """
        keyboard = []

        for row in commands:
            keyboard_row = []
            for command in row:
                keyboard_row.append({"text": command})
            keyboard.append(keyboard_row)

        return {
            "keyboard": keyboard,
            "resize_keyboard": resize_keyboard,
            "one_time_keyboard": one_time_keyboard
        }

    def send_main_keyboard(self, chat_id: Optional[str] = None) -> bool:
        """
        Gửi bàn phím chính với các lệnh thường dùng

        Args:
            chat_id: ID của chat cần gửi (mặc định là admin_id)

        Returns:
            bool: True nếu gửi thành công, False nếu thất bại
        """
        if not chat_id:
            chat_id = self.admin_chat_id

        # Tạo các nhóm lệnh
        commands = [
            ["/status", "/balance", "/orders"],
            ["/watchlist", "/openpositions", "/stats"],
            ["/updatesymbols", "/settings", "/help"],
            ["/pause", "/resume"]
        ]

        # Tạo reply keyboard
        reply_markup = self.create_reply_keyboard(commands)

        # Gửi tin nhắn với bàn phím
        return self.send_message(
            "🎮 <b>BẢNG ĐIỀU KHIỂN BOT</b>\n\nChọn lệnh từ bàn phím bên dưới:",
            chat_id,
            reply_markup
        )

    def start_polling(self):
        """Bắt đầu polling thread để lắng nghe tin nhắn từ Telegram"""
        if self.polling_thread and self.polling_thread.is_alive():
            logger.warning("Polling thread đã đang chạy")
            return

        self.stop_polling = False
        self.polling_thread = threading.Thread(target=self._polling_worker, daemon=True)
        self.polling_thread.start()
        logger.info("Đã bắt đầu polling tin nhắn Telegram")

    def stop_polling_updates(self):
        """Dừng polling thread"""
        self.stop_polling = True
        if self.polling_thread:
            self.polling_thread.join(timeout=5)
            logger.info("Đã dừng polling tin nhắn Telegram")

    def _polling_worker(self):
        """Worker thread để liên tục poll các update từ Telegram"""
        while not self.stop_polling:
            try:
                updates = self._get_updates()
                if updates:
                    for update in updates:
                        if 'message' in update:
                            message = update['message']
                            self._process_message(message)
                time.sleep(1)  # Tránh quá tải API
            except Exception as e:
                logger.error(f"Lỗi trong polling thread: {e}")
                time.sleep(5)  # Nghỉ lâu hơn nếu có lỗi

    def _get_updates(self) -> List[Dict]:
        """Lấy các updates từ Telegram API"""
        try:
            params = {'offset': self.last_update_id + 1, 'timeout': 30}
            response = requests.get(f"{self.base_url}/getUpdates", params=params, timeout=35)
            if response.status_code == 200:
                data = response.json()
                if data['ok'] and data['result']:
                    self.last_update_id = max(update['update_id'] for update in data['result'])
                    return data['result']
            return []
        except Exception as e:
            logger.error(f"Lỗi khi lấy updates: {e}")
            return []

    def _process_message(self, message: Dict):
        """Xử lý tin nhắn nhận được từ Telegram"""
        chat_id = str(message['chat']['id'])

        # Bảo mật: chỉ chấp nhận tin nhắn từ admin
        if chat_id != self.admin_chat_id:
            logger.warning(f"Tin nhắn từ người dùng không được phép: {chat_id}")
            return

        if 'text' not in message:
            return

        text = message['text']
        if text.startswith('/'):
            # Xử lý lệnh
            command_parts = text[1:].split(' ', 1)
            command = command_parts[0].lower()
            args = command_parts[1] if len(command_parts) > 1 else ""

            if command in self.command_handlers:
                try:
                    self.command_handlers[command](chat_id, args, message)
                except Exception as e:
                    logger.error(f"Lỗi khi xử lý lệnh {command}: {e}")
                    self.send_message(f"⚠️ Lỗi xử lý lệnh: {str(e)}", chat_id)
            else:
                self.send_message(f"❓ Lệnh không được hỗ trợ: {command}", chat_id)

    def _handle_start(self, chat_id: str, args: str, message: Dict):
        """Handler cho lệnh /start"""
        user_name = message.get('from', {}).get('first_name', 'Trader')
        response = (
            f"🤖 <b>Chào mừng, {user_name}!</b>\n\n"
            f"Bot giao dịch đã sẵn sàng. Sử dụng /help để xem danh sách lệnh hoặc chọn từ bàn phím bên dưới."
        )
        self.send_message(response, chat_id)

        # Hiển thị bàn phím chính
        self.send_main_keyboard(chat_id)

    def _handle_keyboard(self, chat_id: str, args: str, message: Dict):
        """Handler cho lệnh /keyboard"""
        self.send_message("🎮 <b>Hiển thị bàn phím lệnh</b>", chat_id)
        self.send_main_keyboard(chat_id)

    def _handle_help(self, chat_id: str, args: str, message: Dict):
        """Handler cho lệnh /help"""
        info_commands = [
            "📊 <b>Lệnh thông tin:</b>",
            "/status - Xem thông tin trạng thái bot và tài khoản",
            "/balance - Xem số dư và thông tin equity",
            "/stats - Xem thống kê giao dịch (tỷ lệ thắng/thua, PnL)"
        ]

        manage_commands = [
            "🔧 <b>Lệnh quản lý:</b>",
            "/orders - Xem danh sách lệnh đang mở",
            "/watchlist - Xem danh sách cặp đang theo dõi",
            "/openpositions - Xem các vị thế đang mở"
        ]

        config_commands = [
            "⚙️ <b>Lệnh cấu hình:</b>",
            "/settings - Xem và thay đổi cài đặt bot",
            "/updatesymbols [số lượng] - Cập nhật danh sách symbol từ MEXC (mặc định: 20)",
            "/clearsymbol [symbol] - Xóa symbol khỏi danh sách loại trừ"
        ]

        control_commands = [
            "🎮 <b>Lệnh điều khiển:</b>",
            "/pause - Tạm dừng bot (không đặt lệnh mới)",
            "/resume - Tiếp tục hoạt động của bot",
            "/help - Hiển thị menu trợ giúp này",
            "/keyboard - Hiển thị bàn phím lệnh"
        ]

        all_commands = info_commands + [""] + manage_commands + [""] + config_commands + [""] + control_commands
        response = "<b>📋 MENU LỆNH BOT GIAO DỊCH</b>\n\n" + "\n".join(all_commands)
        self.send_message(response, chat_id)

        # Hiển thị bàn phím chính
        self.send_main_keyboard(chat_id)

    # --- Handlers cho các chức năng quản lý giao dịch ---

    def handle_status(self, chat_id: str, args: str, message: Dict, get_status_callback: Callable = None):
        """Xử lý yêu cầu thông tin trạng thái"""
        if get_status_callback:
            status_info = get_status_callback()
            self.send_message(status_info, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)

    def handle_balance(self, chat_id: str, args: str, message: Dict, get_balance_callback: Callable = None):
        """Xử lý yêu cầu thông tin số dư"""
        if get_balance_callback:
            balance_info = get_balance_callback()
            self.send_message(balance_info, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)

    def handle_orders(self, chat_id: str, args: str, message: Dict, get_orders_callback: Callable = None):
        """Xử lý yêu cầu danh sách lệnh đang mở"""
        if get_orders_callback:
            orders_info = get_orders_callback()
            self.send_message(orders_info, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)

    def handle_watchlist(self, chat_id: str, args: str, message: Dict, get_watchlist_callback: Callable = None):
        """Xử lý yêu cầu danh sách theo dõi"""
        if get_watchlist_callback:
            watchlist_info = get_watchlist_callback()
            self.send_message(watchlist_info, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)

    def handle_open_positions(self, chat_id: str, args: str, message: Dict, get_open_positions_callback: Callable = None):
        """Xử lý yêu cầu open positions"""
        if get_open_positions_callback:
            info = get_open_positions_callback()
            self.send_message(info, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)

    def handle_stats(self, chat_id: str, args: str, message: Dict, get_stats_callback: Callable = None):
        """Xử lý yêu cầu thống kê giao dịch"""
        if get_stats_callback:
            stats_info = get_stats_callback()
            self.send_message(stats_info, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)

    def handle_settings(self, chat_id: str, args: str, message: Dict, get_settings_callback: Callable = None):
        """Xử lý yêu cầu xem/thay đổi cài đặt"""
        if get_settings_callback:
            settings_info = get_settings_callback(args)
            self.send_message(settings_info, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)

    def handle_pause(self, chat_id: str, args: str, message: Dict, pause_callback: Callable = None):
        """Xử lý yêu cầu tạm dừng bot"""
        if pause_callback:
            result = pause_callback()
            self.send_message(result, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)

    def handle_resume(self, chat_id: str, args: str, message: Dict, resume_callback: Callable = None):
        """Xử lý yêu cầu tiếp tục bot"""
        if resume_callback:
            result = resume_callback()
            self.send_message(result, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)

    def handle_clear_symbol(self, chat_id: str, args: str, message: Dict, clear_symbol_callback: Callable = None):
        """Xử lý yêu cầu xóa symbol khỏi danh sách loại trừ"""
        if not args or not args.strip():
            self.send_message("⚠️ Vui lòng cung cấp tên symbol cần xóa. Ví dụ: /clearsymbol BTC_USDT", chat_id)
            return

        symbol = args.strip().upper()
        if clear_symbol_callback:
            result = clear_symbol_callback(symbol)
            self.send_message(result, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)

    def register_trading_commands(self, callbacks):
        """
        Đăng ký các callback xử lý cho từng lệnh liên quan đến giao dịch

        Args:
            callbacks: Dictionary chứa các callback cho từng lệnh
        """
        # Lệnh thông tin
        if 'status' in callbacks:
            self.register_command('status',
                lambda chat_id, args, msg: self.handle_status(chat_id, args, msg, callbacks['status']))

        if 'balance' in callbacks:
            self.register_command('balance',
                lambda chat_id, args, msg: self.handle_balance(chat_id, args, msg, callbacks['balance']))

        if 'stats' in callbacks:
            self.register_command('stats',
                lambda chat_id, args, msg: self.handle_stats(chat_id, args, msg, callbacks['stats']))

        # Lệnh quản lý
        if 'orders' in callbacks:
            self.register_command('orders',
                lambda chat_id, args, msg: self.handle_orders(chat_id, args, msg, callbacks['orders']))

        if 'watchlist' in callbacks:
            self.register_command('watchlist',
                lambda chat_id, args, msg: self.handle_watchlist(chat_id, args, msg, callbacks['watchlist']))

        if 'openpositions' in callbacks:
            self.register_command('openpositions',
                lambda chat_id, args, msg: self.handle_open_positions(chat_id, args, msg, callbacks['openpositions']))

        # Lệnh cấu hình
        if 'settings' in callbacks:
            self.register_command('settings',
                lambda chat_id, args, msg: self.handle_settings(chat_id, args, msg, callbacks['settings']))

        if 'clearsymbol' in callbacks:
            self.register_command('clearsymbol',
                lambda chat_id, args, msg: self.handle_clear_symbol(chat_id, args, msg, callbacks['clearsymbol']))

        # Lệnh điều khiển
        if 'pause' in callbacks:
            self.register_command('pause',
                lambda chat_id, args, msg: self.handle_pause(chat_id, args, msg, callbacks['pause']))

        if 'resume' in callbacks:
            self.register_command('resume',
                lambda chat_id, args, msg: self.handle_resume(chat_id, args, msg, callbacks['resume']))