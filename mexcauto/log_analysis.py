#!/usr/bin/env python3
"""
log_analysis.py

Script to parse logs/trade.log and compute performance metrics:
- Win rate
- Maximum drawdown (MDD)
- Return on Investment (ROI)

Usage:
    python log_analysis.py
"""

import re
import yaml
from datetime import datetime


def load_config(config_path="config.yaml"):
    with open(config_path, "r", encoding="utf-8") as f:
        return yaml.safe_load(f)


def parse_trade_log(log_path="logs/trade.log"):
    # Pattern matches lines with PnL from closed orders
    pattern = re.compile(
        r"^(?P<ts>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - trade - INFO - Đã đóng lệnh (?P<order_id>\d+) cho (?P<symbol>\w+_\w+) \((?P<side>LONG|SHORT)\) với trạng thái (?P<status>[^,]+), PNL: (?P<pnl>[+-]?\d+\.\d+) USDT"
    )
    entries = []
    with open(log_path, "r", encoding="utf-8") as f:
        for line in f:
            m = pattern.match(line.strip())
            if m:
                ts = datetime.strptime(m.group("ts"), "%Y-%m-%d %H:%M:%S,%f")
                pnl = float(m.group("pnl"))
                entries.append({"timestamp": ts, "pnl": pnl})
    return sorted(entries, key=lambda x: x["timestamp"])


def compute_metrics(entries, base_balance):
    total_trades = len(entries)
    wins = sum(1 for e in entries if e["pnl"] > 0)
    losses = total_trades - wins
    win_rate = (wins / total_trades * 100) if total_trades else 0.0

    # Build equity curve
    equity = [base_balance]
    for e in entries:
        equity.append(equity[-1] + e["pnl"])

    # Compute maximum drawdown
    peak = equity[0]
    max_dd = 0.0
    for eq in equity:
        if eq > peak:
            peak = eq
        drawdown = peak - eq
        if drawdown > max_dd:
            max_dd = drawdown
    mdd_abs = max_dd
    mdd_pct = (max_dd / peak * 100) if peak else 0.0

    final_equity = equity[-1]
    roi = ((final_equity - base_balance) / base_balance * 100) if base_balance else 0.0

    return {
        "total_trades": total_trades,
        "wins": wins,
        "losses": losses,
        "win_rate": win_rate,
        "total_pnl": sum(e["pnl"] for e in entries),
        "roi": roi,
        "max_drawdown": mdd_abs,
        "max_drawdown_pct": mdd_pct,
        "final_equity": final_equity
    }


def main():
    config = load_config()
    base_balance = float(config.get("BASE_BALANCE", 0))
    entries = parse_trade_log()
    metrics = compute_metrics(entries, base_balance)

    print("=== Trading Performance Summary ===")
    print(f"Total Trades: {metrics['total_trades']}")
    print(f"Wins: {metrics['wins']}, Losses: {metrics['losses']}, Win Rate: {metrics['win_rate']:.2f}%")
    print(f"Total PnL: {metrics['total_pnl']:.2f} USDT")
    print(f"ROI: {metrics['roi']:.2f}%")
    print(f"Max Drawdown: {metrics['max_drawdown']:.2f} USDT ({metrics['max_drawdown_pct']:.2f}% from peak)")
    print(f"Final Equity: {metrics['final_equity']:.2f} USDT")


if __name__ == "__main__":
    main() 